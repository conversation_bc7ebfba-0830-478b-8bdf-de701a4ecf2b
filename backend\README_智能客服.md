# YUE智能客服系统

## 概述

YUE智能客服系统是一个基于大语言模型的智能客服解决方案，支持带记忆的对话、工具调用和多用户管理。

## 功能特性

### ✅ 已实现功能

1. **大模型集成**
   - 支持DeepSeek模型
   - OpenAI兼容API接口
   - 流式响应输出

2. **记忆管理**
   - 三层记忆架构：聊天历史、公共知识、私有记忆
   - 持久化存储
   - 用户隔离

3. **工具函数**
   - 产品查询：`get_product_details`, `search_products`
   - 订单管理：`get_order_status`, `cancel_order`
   - 促销查询：`get_active_promotions`
   - 售后服务：`check_return_eligibility`, `submit_return_request`
   - 政策查询：`get_policy`

4. **API接口**
   - 流式聊天：`/api/v1/chat/stream`
   - 会话管理：`/api/v1/chat/session/*`
   - 图片上传：`/api/v1/chat/upload-image`

## 快速开始

### 1. 环境配置

确保已安装所有依赖：

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥

编辑 `.env` 文件，配置DeepSeek API密钥：

```env
# OpenAI配置
OPENAI_API_KEY=your-deepseek-api-key
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat
```

### 3. 运行测试

#### 基础集成测试
```bash
python test_basic_integration.py
```

#### 完整功能演示
```bash
python demo_chat_system.py
```

#### LLM集成测试（需要有效API密钥）
```bash
python test_llm_integration.py
```

### 4. 启动服务

#### 启动聊天服务器
```bash
python start_chat_server.py
```

#### 测试聊天客户端
```bash
# 交互式聊天
python test_chat_client.py

# 预定义测试
python test_chat_client.py test
```

## 系统架构

### 核心组件

1. **ChatService** (`app/agents/customer_service/chat_service.py`)
   - 聊天服务主控制器
   - 管理用户会话和记忆
   - 集成AutoGen Agent

2. **MemoryService** (`app/services/memory_service.py`)
   - 记忆服务工厂
   - 三层记忆架构实现
   - 支持向量存储和文本存储

3. **AgentTools** (`app/services/agent_tools.py`)
   - 工具函数集合
   - API调用封装
   - 错误处理

4. **API Router** (`app/api/v1/customer/customer.py`)
   - FastAPI路由定义
   - 流式响应处理
   - 会话管理

### 记忆架构

```
用户记忆系统
├── 聊天历史记忆 (ChatMemoryService)
│   ├── 用户对话历史
│   └── 持久化到本地文件
├── 公共记忆 (PublicMemoryService)
│   ├── 共享知识库
│   └── 向量数据库存储
└── 私有记忆 (PrivateMemoryService)
    ├── 用户个人信息
    └── 向量数据库存储
```

## API文档

### 聊天接口

#### POST /api/v1/chat/stream
流式聊天接口

**请求体：**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "user_id": "user123",
  "session_id": "session456"
}
```

**响应：**
```
data: {"content": "你好"}
data: {"content": "！"}
data: {"content": "我是"}
...
```

#### POST /api/v1/chat/session/create
创建新会话

**请求体：**
```json
{
  "user_id": "user123"
}
```

**响应：**
```json
{
  "session_id": "uuid",
  "user_id": "user123",
  "created_at": "2024-12-02T10:00:00",
  "last_active": "2024-12-02T10:00:00",
  "messages": []
}
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | DeepSeek API密钥 | 必填 |
| `OPENAI_API_BASE` | API端点 | `https://api.deepseek.com/v1` |
| `LLM_MODEL` | 模型名称 | `deepseek-chat` |
| `HOST` | 服务器地址 | `127.0.0.1` |
| `PORT` | 服务器端口 | `8000` |

### 目录结构

```
backend/
├── app/
│   ├── agents/customer_service/    # 智能客服代理
│   ├── api/v1/customer/           # API接口
│   ├── core/                      # 核心配置
│   ├── schemas/                   # 数据模型
│   └── services/                  # 服务层
├── data/                          # 数据存储
│   ├── memories/                  # 记忆数据
│   └── sessions/                  # 会话数据
├── logs/                          # 日志文件
└── tests/                         # 测试文件
```

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查 `.env` 文件中的 `OPENAI_API_KEY`
   - 确保使用有效的DeepSeek API密钥

2. **模块导入错误**
   - 运行 `python test_basic_integration.py` 检查依赖
   - 确保所有依赖已正确安装

3. **记忆服务错误**
   - 检查 `data/memories` 目录权限
   - 确保ChromaDB正常工作

4. **端口占用**
   - 修改 `.env` 中的 `PORT` 配置
   - 或停止占用端口的其他服务

### 日志查看

- 聊天日志：`logs/chat/memory.log`
- 会话日志：`logs/sessions/sessions.log`
- 应用日志：`logs/app.log`

## 开发指南

### 添加新工具函数

1. 在 `app/services/agent_tools.py` 中定义函数
2. 在 `ChatService` 的 `tools` 列表中注册
3. 添加相应的API接口调用

### 扩展记忆功能

1. 继承 `MemoryService` 基类
2. 实现 `add`, `query`, `clear`, `close` 方法
3. 在 `MemoryServiceFactory` 中注册

### 自定义系统提示

修改 `ChatService` 中的 `default_system_message` 变量。

## 许可证

本项目采用 MIT 许可证。
