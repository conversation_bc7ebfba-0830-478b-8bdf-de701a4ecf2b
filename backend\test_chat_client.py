#!/usr/bin/env python3
"""
聊天客户端测试脚本
用于测试智能客服聊天API
"""

import asyncio
import aiohttp
import json
import sys
from typing import AsyncGenerator

async def test_chat_stream(message: str, user_id: str = "test_user", session_id: str = None):
    """测试流式聊天"""
    url = "http://127.0.0.1:8000/api/v1/chat/stream"
    
    # 构建请求数据
    request_data = {
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ],
        "user_id": user_id,
        "session_id": session_id
    }
    
    print(f"📤 发送消息: {message}")
    print("📥 AI回复: ", end="", flush=True)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    # 处理流式响应
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                try:
                                    data = json.loads(line_str[6:])  # 去掉 'data: ' 前缀
                                    content = data.get('content', '')
                                    print(content, end="", flush=True)
                                except json.JSONDecodeError:
                                    continue
                    print("\n")
                else:
                    print(f"❌ 请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_session_creation(user_id: str = "test_user"):
    """测试会话创建"""
    url = "http://127.0.0.1:8000/api/v1/chat/session/create"
    
    request_data = {
        "user_id": user_id
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=request_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 会话创建成功: {data['session_id']}")
                    return data['session_id']
                else:
                    print(f"❌ 会话创建失败: {response.status}")
                    return None
                    
    except Exception as e:
        print(f"❌ 会话创建连接失败: {e}")
        return None

async def test_health_check():
    """测试健康检查"""
    url = "http://127.0.0.1:8000/health"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 服务健康: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 健康检查连接失败: {e}")
        return False

async def interactive_chat():
    """交互式聊天"""
    print("🤖 YUE智能客服聊天测试")
    print("=" * 50)
    
    # 检查服务健康状态
    print("🔍 检查服务状态...")
    if not await test_health_check():
        print("❌ 服务不可用，请先启动聊天服务器")
        return
    
    # 创建会话
    print("\n📝 创建聊天会话...")
    session_id = await test_session_creation()
    if not session_id:
        print("❌ 无法创建会话")
        return
    
    print(f"\n💬 开始聊天 (会话ID: {session_id})")
    print("输入 'quit' 或 'exit' 退出聊天")
    print("-" * 50)
    
    user_id = "test_user"
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            # 发送消息并获取回复
            await test_chat_stream(user_input, user_id, session_id)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 聊天错误: {e}")

async def run_predefined_tests():
    """运行预定义测试"""
    print("🧪 运行预定义聊天测试")
    print("=" * 50)
    
    # 检查服务健康状态
    print("🔍 检查服务状态...")
    if not await test_health_check():
        print("❌ 服务不可用，请先启动聊天服务器")
        return
    
    # 创建会话
    print("\n📝 创建聊天会话...")
    session_id = await test_session_creation()
    if not session_id:
        print("❌ 无法创建会话")
        return
    
    # 测试消息列表
    test_messages = [
        "你好，请介绍一下你自己",
        "我想了解一下退换货政策",
        "请帮我查询产品ID为1的商品详情",
        "有什么促销活动吗？",
        "我的订单号是12345，请帮我查询订单状态"
    ]
    
    user_id = "test_user"
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📋 测试 {i}/{len(test_messages)}")
        await test_chat_stream(message, user_id, session_id)
        await asyncio.sleep(1)  # 等待1秒再发送下一条消息

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行预定义测试
        asyncio.run(run_predefined_tests())
    else:
        # 交互式聊天
        asyncio.run(interactive_chat())

if __name__ == "__main__":
    main()
