#!/usr/bin/env python3
"""
简单的聊天服务器启动脚本
用于测试智能客服聊天功能
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 导入配置和路由
from app.core.config import settings
from app.api.v1.customer.customer import router as customer_router

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="YUE智能客服聊天服务",
    description="智能客服聊天API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(customer_router, prefix="/api/v1/chat", tags=["聊天"])

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "智能客服聊天服务",
        "version": "1.0.0"
    }

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "YUE智能客服聊天服务",
        "docs": "/docs",
        "health": "/health",
        "chat_api": "/api/v1/chat"
    }

if __name__ == "__main__":
    print("🚀 启动YUE智能客服聊天服务")
    print(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📖 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"💬 聊天API: http://{settings.HOST}:{settings.PORT}/api/v1/chat")
    print("-" * 60)
    
    # 启动服务器
    uvicorn.run(
        "start_chat_server:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
