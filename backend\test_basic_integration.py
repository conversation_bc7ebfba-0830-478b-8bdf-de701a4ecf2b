#!/usr/bin/env python3
"""
基础集成测试脚本 - 不依赖真实API调用
测试模块导入和基础配置
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_imports():
    """测试模块导入"""
    print("🔧 测试模块导入")
    print("-" * 50)
    
    try:
        # 测试配置导入
        from app.core.config import settings
        print(f"✅ 配置模块导入成功")
        print(f"   - 项目名称: {settings.PROJECT_NAME}")
        print(f"   - API密钥配置: {'已配置' if settings.OPENAI_API_KEY else '未配置'}")
        print(f"   - API端点: {settings.OPENAI_API_BASE}")
        print(f"   - 模型: {settings.LLM_MODEL}")
        
        # 测试LLM客户端导入
        from app.core.llms import model_client
        print(f"✅ LLM客户端导入成功: {type(model_client).__name__}")
        
        # 测试聊天服务导入
        from app.agents.customer_service.chat_service import ChatService
        print(f"✅ 聊天服务导入成功")
        
        # 测试记忆服务导入
        from app.services.memory_service import MemoryServiceFactory
        print(f"✅ 记忆服务导入成功")
        
        # 测试工具函数导入
        from app.services import agent_tools
        print(f"✅ 工具函数导入成功")
        
        # 测试API模块导入
        from app.api.v1.customer.customer import router
        print(f"✅ API路由导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_memory_service():
    """测试记忆服务基础功能"""
    print("\n🧠 测试记忆服务基础功能")
    print("-" * 50)
    
    try:
        from app.services.memory_service import MemoryServiceFactory
        
        # 创建记忆服务工厂
        factory = MemoryServiceFactory()
        print("✅ 记忆服务工厂创建成功")
        
        # 测试聊天记忆服务
        chat_memory = factory.get_chat_memory_service("test_user")
        print("✅ 聊天记忆服务创建成功")
        
        # 测试公共记忆服务
        public_memory = factory.get_public_memory_service()
        print("✅ 公共记忆服务创建成功")
        
        # 测试私有记忆服务
        private_memory = factory.get_private_memory_service("test_user")
        print("✅ 私有记忆服务创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 记忆服务测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_chat_service_creation():
    """测试聊天服务创建"""
    print("\n💬 测试聊天服务创建")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        
        # 创建聊天服务
        chat_service = ChatService()
        print("✅ 聊天服务创建成功")
        
        # 检查工具函数
        print(f"✅ 工具函数数量: {len(chat_service.tools)}")
        for i, tool in enumerate(chat_service.tools):
            print(f"   - 工具 {i+1}: {tool.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 聊天服务创建失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_schemas():
    """测试数据模型"""
    print("\n📋 测试数据模型")
    print("-" * 50)
    
    try:
        from app.schemas.customer import ChatMessage, ChatRequest, SessionRequest
        
        # 测试ChatMessage
        message = ChatMessage(role="user", content="测试消息")
        print(f"✅ ChatMessage创建成功: {message.role} - {message.content}")
        
        # 测试ChatRequest
        request = ChatRequest(
            messages=[message],
            user_id="test_user"
        )
        print(f"✅ ChatRequest创建成功: {len(request.messages)} 条消息")
        
        # 测试SessionRequest
        session_req = SessionRequest(user_id="test_user")
        print(f"✅ SessionRequest创建成功: {session_req.user_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_tools():
    """测试工具函数"""
    print("\n🔧 测试工具函数")
    print("-" * 50)
    
    try:
        from app.services import agent_tools
        
        # 测试工具函数是否可调用
        tools = [
            agent_tools.get_active_promotions,
            agent_tools.get_order_status,
            agent_tools.get_policy,
            agent_tools.get_product_details,
            agent_tools.search_products,
            agent_tools.submit_return_request,
            agent_tools.check_return_eligibility,
            agent_tools.cancel_order
        ]
        
        print(f"✅ 发现 {len(tools)} 个工具函数:")
        for tool in tools:
            print(f"   - {tool.__name__}")
        
        # 测试一个简单的工具函数调用（不需要网络）
        policy_result = agent_tools.get_policy("return")
        print(f"✅ 策略查询测试成功: {policy_result[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def main():
    """主测试函数"""
    print("🚀 基础集成测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模块导入
    result1 = await test_imports()
    test_results.append(("模块导入", result1))
    
    # 2. 测试记忆服务
    result2 = await test_memory_service()
    test_results.append(("记忆服务", result2))
    
    # 3. 测试聊天服务创建
    result3 = await test_chat_service_creation()
    test_results.append(("聊天服务创建", result3))
    
    # 4. 测试数据模型
    result4 = await test_schemas()
    test_results.append(("数据模型", result4))
    
    # 5. 测试工具函数
    result5 = await test_tools()
    test_results.append(("工具函数", result5))
    
    # 输出测试总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！模块集成成功！")
        print("\n📝 下一步:")
        print("   1. 配置有效的API密钥")
        print("   2. 运行完整的LLM集成测试")
        print("   3. 启动API服务器进行端到端测试")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，但仍有问题需要解决")
    else:
        print("❌ 多项测试失败，需要检查配置和代码")

if __name__ == "__main__":
    # 确保在正确的目录中运行
    os.chdir(Path(__file__).parent)
    
    # 运行测试
    asyncio.run(main())
