#!/usr/bin/env python3
"""
智能客服系统演示脚本
展示带记忆的聊天功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.agents.customer_service.chat_service import ChatService
from app.schemas.customer import ChatMessage

async def demo_basic_chat():
    """演示基础聊天功能"""
    print("💬 演示基础聊天功能")
    print("=" * 50)
    
    # 创建聊天服务
    chat_service = ChatService()
    
    # 测试消息
    messages = [
        "你好，请介绍一下你自己",
        "我想了解一下你们的服务",
        "谢谢你的介绍"
    ]
    
    user_id = "demo_user"
    
    for i, message_text in enumerate(messages, 1):
        print(f"\n📤 消息 {i}: {message_text}")
        print("📥 AI回复: ", end="", flush=True)
        
        # 创建消息对象
        message = ChatMessage(role="user", content=message_text)
        
        try:
            # 获取流式回复
            async for chunk in chat_service.chat_stream(
                messages=[message],
                user_id=user_id
            ):
                print(chunk, end="", flush=True)
            print("\n")
            
        except Exception as e:
            print(f"❌ 聊天失败: {e}")
        
        # 等待一下再发送下一条消息
        await asyncio.sleep(1)

async def demo_memory_chat():
    """演示记忆功能"""
    print("\n🧠 演示记忆功能")
    print("=" * 50)
    
    # 创建聊天服务
    chat_service = ChatService()
    user_id = "memory_demo_user"
    
    # 第一轮对话 - 告诉AI一些信息
    print("\n📤 第一轮: 我的名字是张三，我住在北京")
    print("📥 AI回复: ", end="", flush=True)
    
    message1 = ChatMessage(role="user", content="我的名字是张三，我住在北京，请记住这些信息")
    
    try:
        async for chunk in chat_service.chat_stream(
            messages=[message1],
            user_id=user_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
    except Exception as e:
        print(f"❌ 第一轮对话失败: {e}")
    
    # 等待确保记忆保存
    await asyncio.sleep(2)
    
    # 第二轮对话 - 测试记忆
    print("\n📤 第二轮: 你还记得我的名字和住址吗？")
    print("📥 AI回复: ", end="", flush=True)
    
    message2 = ChatMessage(role="user", content="你还记得我的名字和住址吗？")
    
    try:
        async for chunk in chat_service.chat_stream(
            messages=[message2],
            user_id=user_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
    except Exception as e:
        print(f"❌ 第二轮对话失败: {e}")

async def demo_tool_usage():
    """演示工具使用功能"""
    print("\n🔧 演示工具使用功能")
    print("=" * 50)
    
    # 创建聊天服务
    chat_service = ChatService()
    user_id = "tool_demo_user"
    
    # 测试不同的工具调用
    tool_messages = [
        "请帮我查询产品ID为1的商品详情",
        "我想了解一下退换货政策",
        "有什么促销活动吗？",
        "请帮我查询订单号12345的状态"
    ]
    
    for i, message_text in enumerate(tool_messages, 1):
        print(f"\n📤 工具测试 {i}: {message_text}")
        print("📥 AI回复: ", end="", flush=True)
        
        message = ChatMessage(role="user", content=message_text)
        
        try:
            async for chunk in chat_service.chat_stream(
                messages=[message],
                user_id=user_id
            ):
                print(chunk, end="", flush=True)
            print("\n")
        except Exception as e:
            print(f"❌ 工具测试 {i} 失败: {e}")
        
        # 等待一下再发送下一条消息
        await asyncio.sleep(1)

async def demo_multi_user():
    """演示多用户功能"""
    print("\n👥 演示多用户功能")
    print("=" * 50)
    
    # 创建聊天服务
    chat_service = ChatService()
    
    # 用户A的对话
    print("\n👤 用户A的对话:")
    user_a_id = "user_a"
    message_a = ChatMessage(role="user", content="我是用户A，我喜欢电子产品")
    
    print("📤 用户A: 我是用户A，我喜欢电子产品")
    print("📥 AI回复: ", end="", flush=True)
    
    try:
        async for chunk in chat_service.chat_stream(
            messages=[message_a],
            user_id=user_a_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
    except Exception as e:
        print(f"❌ 用户A对话失败: {e}")
    
    # 用户B的对话
    print("\n👤 用户B的对话:")
    user_b_id = "user_b"
    message_b = ChatMessage(role="user", content="我是用户B，我喜欢服装")
    
    print("📤 用户B: 我是用户B，我喜欢服装")
    print("📥 AI回复: ", end="", flush=True)
    
    try:
        async for chunk in chat_service.chat_stream(
            messages=[message_b],
            user_id=user_b_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
    except Exception as e:
        print(f"❌ 用户B对话失败: {e}")
    
    # 验证用户记忆隔离
    await asyncio.sleep(1)
    
    print("\n🔍 验证用户记忆隔离:")
    
    # 用户A询问用户B的信息
    print("📤 用户A: 你知道用户B喜欢什么吗？")
    print("📥 AI回复: ", end="", flush=True)
    
    message_a2 = ChatMessage(role="user", content="你知道用户B喜欢什么吗？")
    
    try:
        async for chunk in chat_service.chat_stream(
            messages=[message_a2],
            user_id=user_a_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
    except Exception as e:
        print(f"❌ 用户A第二次对话失败: {e}")

async def main():
    """主演示函数"""
    print("🚀 YUE智能客服系统演示")
    print("🤖 展示带记忆的智能聊天功能")
    print("=" * 60)
    
    try:
        # 1. 基础聊天功能演示
        await demo_basic_chat()
        
        # 2. 记忆功能演示
        await demo_memory_chat()
        
        # 3. 工具使用功能演示
        await demo_tool_usage()
        
        # 4. 多用户功能演示
        await demo_multi_user()
        
        print("\n🎉 演示完成！")
        print("=" * 60)
        print("✅ 智能客服系统功能:")
        print("   - ✅ 基础聊天对话")
        print("   - ✅ 用户记忆管理")
        print("   - ✅ 工具函数调用")
        print("   - ✅ 多用户隔离")
        print("   - ✅ 流式响应输出")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 确保在正确的目录中运行
    os.chdir(Path(__file__).parent)
    
    # 运行演示
    asyncio.run(main())
